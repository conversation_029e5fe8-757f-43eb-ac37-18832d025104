/**
 * HierarchicalBlackboard.ts
 *
 * Base class for hierarchical blackboards that can communicate with parent and child blackboards.
 * This enables a scalable communication architecture from local agent blackboards to global HyperBlackboard.
 */

import { BlackboardInterface } from './BlackboardInterface';
import { AgentRegistration, AgentStatus, AgentMessage, AgentTask } from '../../agents/agent-base/types';
import { v4 as uuidv4 } from 'uuid';
import { EventEmitter } from 'events';

/**
 * Blackboard propagation direction
 */
export const PropagationDirection = {
  UP: 'up',
  DOWN: 'down',
  BOTH: 'both',
  NONE: 'none'
} as const;

export type PropagationDirection = typeof PropagationDirection[keyof typeof PropagationDirection];

/**
 * Blackboard propagation policy
 */
export interface PropagationPolicy {
  direction: PropagationDirection;
  topicPatterns: string[]; // Regex patterns for topics to propagate
  excludePatterns?: string[]; // Regex patterns for topics to exclude
  transformFunction?: (topic: string, data: any, direction: PropagationDirection) => { topic: string; data: any } | null;
}

/**
 * Hierarchical blackboard configuration
 */
export interface HierarchicalBlackboardConfig {
  id: string;
  name: string;
  level: 'agent' | 'node' | 'regional' | 'global';
  propagationPolicies: PropagationPolicy[];
  maxMessageRetention?: number;
  enableMessageExpiration?: boolean;
  messageExpirationMs?: number;
  enableEventHistory?: boolean;
  maxEventHistorySize?: number;
  enableBiologicalIntegration?: boolean;
}

/**
 * Hierarchical blackboard class
 */
export abstract class HierarchicalBlackboard implements BlackboardInterface {
  protected id: string;
  protected name: string;
  protected level: string;
  protected config: HierarchicalBlackboardConfig;
  protected parentBlackboard: HierarchicalBlackboard | null = null;
  protected childBlackboards: Map<string, HierarchicalBlackboard> = new Map();
  protected propagationPolicies: PropagationPolicy[];

  // Core blackboard data
  protected agents: Map<string, AgentRegistration> = new Map();
  protected messages: Map<string, AgentMessage> = new Map();
  protected tasks: Map<string, AgentTask[]> = new Map();
  protected sharedData: Map<string, { value: any; metadata?: Record<string, any> }> = new Map();

  // Subscription system
  protected subscribers: Map<string, Array<(topic: string, data: any) => void>> = new Map();
  protected eventEmitter: EventEmitter = new EventEmitter();

  // Event history
  protected eventHistory: Array<{ topic: string; data: any; timestamp: Date }> = [];

  // Global propagation tracking to prevent infinite loops
  private static activePropagations: Set<string> = new Set();
  private static maxPropagationDepth: number = 5;

  /**
   * Constructor
   */
  constructor(config: HierarchicalBlackboardConfig) {
    this.id = config.id;
    this.name = config.name;
    this.level = config.level;
    this.propagationPolicies = config.propagationPolicies;

    // Set default configuration
    this.config = {
      ...config,
      maxMessageRetention: config.maxMessageRetention || 1000,
      enableMessageExpiration: config.enableMessageExpiration !== undefined ? config.enableMessageExpiration : true,
      messageExpirationMs: config.messageExpirationMs || 3600000, // 1 hour
      enableEventHistory: config.enableEventHistory !== undefined ? config.enableEventHistory : true,
      maxEventHistorySize: config.maxEventHistorySize || 1000,
      enableBiologicalIntegration: config.enableBiologicalIntegration !== undefined ? config.enableBiologicalIntegration : true
    };

    // Set max listeners to avoid memory leak warnings
    this.eventEmitter.setMaxListeners(100);
  }

  /**
   * Get blackboard ID
   */
  public getId(): string {
    return this.id;
  }

  /**
   * Get blackboard name
   */
  public getName(): string {
    return this.name;
  }

  /**
   * Get blackboard level
   */
  public getLevel(): string {
    return this.level;
  }

  /**
   * Set parent blackboard
   */
  public setParentBlackboard(blackboard: HierarchicalBlackboard | null): void {
    this.parentBlackboard = blackboard;
  }

  /**
   * Get parent blackboard
   */
  public getParentBlackboard(): HierarchicalBlackboard | null {
    return this.parentBlackboard;
  }

  /**
   * Add child blackboard
   */
  public addChildBlackboard(blackboard: HierarchicalBlackboard): void {
    this.childBlackboards.set(blackboard.getId(), blackboard);
    blackboard.setParentBlackboard(this);
  }

  /**
   * Remove child blackboard
   */
  public removeChildBlackboard(blackboardId: string): void {
    const blackboard = this.childBlackboards.get(blackboardId);

    if (blackboard) {
      blackboard.setParentBlackboard(null);
      this.childBlackboards.delete(blackboardId);
    }
  }

  /**
   * Get child blackboard
   */
  public getChildBlackboard(blackboardId: string): HierarchicalBlackboard | undefined {
    return this.childBlackboards.get(blackboardId);
  }

  /**
   * Get all child blackboards
   */
  public getChildBlackboards(): Map<string, HierarchicalBlackboard> {
    return new Map(this.childBlackboards);
  }

  /**
   * Register a new agent with the blackboard
   */
  public registerAgent(agent: AgentRegistration): void {
    this.agents.set(agent.id, agent);
    this.notifySubscribers('agent:registered', agent);
  }

  /**
   * Unregister an agent from the blackboard
   */
  public unregisterAgent(agentId: string): void {
    const agent = this.agents.get(agentId);

    if (agent) {
      this.agents.delete(agentId);
      this.notifySubscribers('agent:unregistered', { agentId, agent });
    }
  }

  /**
   * Update an agent's status on the blackboard
   */
  public updateAgentStatus(agentId: string, status: AgentStatus): void {
    const agent = this.agents.get(agentId);

    if (agent) {
      const oldStatus = agent.status;
      agent.status = status;
      this.agents.set(agentId, agent);

      this.notifySubscribers('agent:status_updated', {
        agentId,
        oldStatus,
        newStatus: status,
        timestamp: Date.now()
      });
    }
  }

  /**
   * Update an agent's tasks on the blackboard
   */
  public updateAgentTasks(agentId: string, tasks: AgentTask[]): void {
    this.tasks.set(agentId, tasks);

    this.notifySubscribers('agent:tasks_updated', {
      agentId,
      tasks,
      timestamp: Date.now()
    });
  }

  /**
   * Post a message to the blackboard
   */
  public postMessage(message: AgentMessage): void {
    // Ensure message has an ID
    if (!message.id) {
      message.id = uuidv4();
    }

    // Ensure message has a timestamp
    if (!message.timestamp) {
      message.timestamp = new Date();
    }

    // Add expiration if enabled
    if (this.config.enableMessageExpiration && !message.expiresAt) {
      const expirationTime = new Date();
      expirationTime.setTime(expirationTime.getTime() + this.config.messageExpirationMs!);
      message.expiresAt = expirationTime;
    }

    // Store message
    this.messages.set(message.id, message);

    // Trim messages if needed
    if (this.messages.size > this.config.maxMessageRetention!) {
      // Remove oldest messages
      const messagesToRemove = this.messages.size - this.config.maxMessageRetention!;
      const messageIds = Array.from(this.messages.keys());

      for (let i = 0; i < messagesToRemove; i++) {
        this.messages.delete(messageIds[i]);
      }
    }

    // Notify subscribers
    this.notifySubscribers('message:posted', message);

    // Propagate message if needed
    this.propagateMessage('message:posted', message);
  }

  /**
   * Get all messages intended for a specific agent
   */
  public getMessagesForAgent(agentId: string): AgentMessage[] {
    return Array.from(this.messages.values()).filter(
      message => message.recipientId === agentId || message.recipientId === undefined
    );
  }

  /**
   * Mark a message as read by an agent
   */
  public markMessageAsRead(messageId: string, agentId: string): void {
    const message = this.messages.get(messageId);

    if (message) {
      if (!message.readBy) {
        message.readBy = [];
      }

      if (!message.readBy.includes(agentId)) {
        message.readBy.push(agentId);
        this.messages.set(messageId, message);

        this.notifySubscribers('message:read', {
          messageId,
          agentId,
          timestamp: Date.now()
        });
      }
    }
  }

  /**
   * Get all registered agents
   */
  public getRegisteredAgents(): AgentRegistration[] {
    return Array.from(this.agents.values());
  }

  /**
   * Get a specific agent by ID
   */
  public getAgentById(agentId: string): AgentRegistration | undefined {
    return this.agents.get(agentId);
  }

  /**
   * Get all agents of a specific type
   */
  public getAgentsByType(type: string): AgentRegistration[] {
    return Array.from(this.agents.values()).filter(agent => agent.type === type);
  }

  /**
   * Get all tasks for a specific agent
   */
  public getTasksForAgent(agentId: string): AgentTask[] {
    return this.tasks.get(agentId) || [];
  }

  /**
   * Add a shared data item to the blackboard
   */
  public setSharedData(key: string, value: any, metadata?: Record<string, any>): void {
    this.sharedData.set(key, { value, metadata });

    this.notifySubscribers('shared_data:updated', {
      key,
      value,
      metadata,
      timestamp: Date.now()
    });

    // Propagate shared data if needed
    this.propagateMessage('shared_data:updated', {
      key,
      value,
      metadata,
      timestamp: Date.now()
    });
  }

  /**
   * Get a shared data item from the blackboard
   */
  public getSharedData(key: string): any {
    const data = this.sharedData.get(key);
    return data ? data.value : undefined;
  }

  /**
   * Get shared data metadata
   */
  public getSharedDataMetadata(key: string): Record<string, any> | undefined {
    const data = this.sharedData.get(key);
    return data ? data.metadata : undefined;
  }

  /**
   * Get all shared data
   */
  public getAllSharedData(): Record<string, any> {
    const result: Record<string, any> = {};
    for (const [key, data] of Array.from(this.sharedData.entries())) {
      result[key] = data.value;
    }
    return result;
  }

  /**
   * Get shared data with version information
   */
  public getSharedDataWithVersion(key: string): { value: any; version: number; timestamp: number; causalHash?: string; metadata?: Record<string, any> } | undefined {
    const data = this.sharedData.get(key);
    if (!data) return undefined;

    return {
      value: data.value,
      version: 1,
      timestamp: Date.now(),
      metadata: data.metadata
    };
  }

  /**
   * Get all shared data with version information
   */
  public getAllSharedDataWithVersion(): Record<string, { value: any; version: number; timestamp: number; causalHash?: string; metadata?: Record<string, any> }> {
    const result: Record<string, { value: any; version: number; timestamp: number; causalHash?: string; metadata?: Record<string, any> }> = {};
    for (const [key, data] of Array.from(this.sharedData.entries())) {
      result[key] = {
        value: data.value,
        version: 1,
        timestamp: Date.now(),
        metadata: data.metadata
      };
    }
    return result;
  }

  /**
   * Remove shared data
   */
  public removeSharedData(key: string): void {
    this.sharedData.delete(key);
  }

  /**
   * Subscribe to a topic
   */
  public subscribe(topic: string, callback: (topic: string, data: any) => void, options?: any): string {
    if (!this.subscribers.has(topic)) {
      this.subscribers.set(topic, []);
    }

    this.subscribers.get(topic)!.push(callback);
    this.eventEmitter.on(topic, (data: any) => callback(topic, data));

    // Return a subscription ID
    return `${topic}-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Unsubscribe from a topic
   */
  public unsubscribe(subscriptionId: string): boolean {
    // For now, just return true as a placeholder
    // In a real implementation, we'd track subscription IDs
    return true;
  }

  /**
   * Publish an event to the blackboard
   */
  public publish(topic: string, data: any): void {
    // Add to event history if enabled
    if (this.config.enableEventHistory) {
      this.eventHistory.push({
        topic,
        data,
        timestamp: new Date()
      });

      // Trim event history if needed
      if (this.eventHistory.length > this.config.maxEventHistorySize!) {
        this.eventHistory.shift();
      }
    }

    // Notify subscribers
    this.notifySubscribers(topic, data);

    // Propagate event if needed
    this.propagateMessage(topic, data);
  }

  /**
   * Notify subscribers of an event
   */
  protected notifySubscribers(topic: string, data: any): void {
    // Emit event
    this.eventEmitter.emit(topic, data);

    // Also emit wildcard events
    this.eventEmitter.emit('*', topic, data);

    // Handle topic patterns (e.g., 'agent:*')
    const topicParts = topic.split(':');

    if (topicParts.length > 1) {
      const wildcardTopic = `${topicParts[0]}:*`;
      this.eventEmitter.emit(wildcardTopic, data);
    }
  }

  /**
   * Propagate a message to parent and child blackboards
   */
  protected propagateMessage(topic: string, data: any): void {
    // Check if propagation should be skipped
    if (data._source?._skipPropagation) {
      return;
    }

    // Check for propagation loops using global tracking
    const propagationKey = `${this.id}:${topic}:${JSON.stringify(data).substring(0, 100)}`;
    if (HierarchicalBlackboard.activePropagations.has(propagationKey)) {
      // Loop detected, stop propagation
      return;
    }

    // Check propagation depth
    const propagationPath = data._source?.propagationPath || [];
    if (propagationPath.length >= HierarchicalBlackboard.maxPropagationDepth) {
      // Max depth reached, stop propagation
      return;
    }

    if (propagationPath.includes(this.id)) {
      // Loop detected, stop propagation
      return;
    }

    // Add to active propagations
    HierarchicalBlackboard.activePropagations.add(propagationKey);

    try {

    // Check each propagation policy
    for (const policy of this.propagationPolicies) {
      // Check if topic matches any pattern
      const shouldPropagate = policy.topicPatterns.some(pattern => {
        const regex = new RegExp(pattern);
        return regex.test(topic);
      });

      // Check if topic is excluded
      const isExcluded = policy.excludePatterns?.some(pattern => {
        const regex = new RegExp(pattern);
        return regex.test(topic);
      });

      if (shouldPropagate && !isExcluded) {
        // Transform data if needed
        let transformedUp = { topic, data };
        let transformedDown = { topic, data };

        if (policy.transformFunction) {
          transformedUp = policy.transformFunction(topic, data, PropagationDirection.UP) || { topic, data };
          transformedDown = policy.transformFunction(topic, data, PropagationDirection.DOWN) || { topic, data };
        }

        // Add propagation tracking
        const enhancedDataUp = {
          ...transformedUp.data,
          _source: {
            ...transformedUp.data._source,
            propagationPath: [...propagationPath, this.id]
          }
        };

        const enhancedDataDown = {
          ...transformedDown.data,
          _source: {
            ...transformedDown.data._source,
            propagationPath: [...propagationPath, this.id]
          }
        };

        // Propagate up to parent
        if (
          (policy.direction === PropagationDirection.UP || policy.direction === PropagationDirection.BOTH) &&
          this.parentBlackboard
        ) {
          this.parentBlackboard.receiveFromChild(this.id, transformedUp.topic, enhancedDataUp);
        }

        // Propagate down to children
        if (
          (policy.direction === PropagationDirection.DOWN || policy.direction === PropagationDirection.BOTH) &&
          this.childBlackboards.size > 0
        ) {
          for (const childBlackboard of Array.from(this.childBlackboards.values())) {
            childBlackboard.receiveFromParent(transformedDown.topic, enhancedDataDown);
          }
        }
      }
    } finally {
      // Always remove from active propagations
      HierarchicalBlackboard.activePropagations.delete(propagationKey);
    }
  }

  /**
   * Receive a message from a child blackboard
   */
  public receiveFromChild(childId: string, topic: string, data: any): void {
    // Check for propagation loops
    const propagationPath = data._source?.propagationPath || [];
    if (propagationPath.includes(this.id)) {
      // Loop detected, stop propagation
      return;
    }

    // Add source information
    const dataWithSource = {
      ...data,
      _source: {
        blackboardId: childId,
        blackboardLevel: this.childBlackboards.get(childId)?.getLevel(),
        propagationPath: [...propagationPath, childId],
        _skipPropagation: true // Prevent further propagation
      }
    };

    // Publish to local subscribers only (no propagation)
    this.notifySubscribers(topic, dataWithSource);
  }

  /**
   * Receive a message from the parent blackboard
   */
  public receiveFromParent(topic: string, data: any): void {
    // Check for propagation loops
    const propagationPath = data._source?.propagationPath || [];
    if (propagationPath.includes(this.id)) {
      // Loop detected, stop propagation
      return;
    }

    // Add source information
    const dataWithSource = {
      ...data,
      _source: {
        blackboardId: this.parentBlackboard?.getId(),
        blackboardLevel: this.parentBlackboard?.getLevel(),
        propagationPath: [...propagationPath, this.parentBlackboard?.getId()],
        _skipPropagation: true // Prevent further propagation
      }
    };

    // Publish to local subscribers only (no propagation)
    this.notifySubscribers(topic, dataWithSource);
  }

  /**
   * Get event history
   */
  public getEventHistory(): Array<{ topic: string; data: any; timestamp: Date }> {
    return [...this.eventHistory];
  }

  /**
   * Clear event history
   */
  public clearEventHistory(): void {
    this.eventHistory = [];
  }

  /**
   * Clean up expired messages
   */
  public cleanupExpiredMessages(): void {
    if (!this.config.enableMessageExpiration) {
      return;
    }

    const now = new Date();
    const expiredMessages: string[] = [];

    for (const [id, message] of Array.from(this.messages.entries())) {
      if (message.expiresAt && message.expiresAt < now) {
        expiredMessages.push(id);
      }
    }

    for (const id of expiredMessages) {
      const message = this.messages.get(id);
      this.messages.delete(id);

      if (message) {
        this.notifySubscribers('message:expired', {
          messageId: id,
          message,
          timestamp: now
        });
      }
    }
  }

  /**
   * Get messages for a topic
   */
  public getMessages(topic: string, limit?: number): { topic: string; data: any; timestamp: Date }[] {
    return this.eventHistory
      .filter(event => event.topic === topic)
      .slice(0, limit)
      .map(event => ({
        topic: event.topic,
        data: event.data,
        timestamp: event.timestamp
      }));
  }

  /**
   * Clear messages for a topic
   */
  public clearMessages(topic: string): void {
    this.eventHistory = this.eventHistory.filter(event => event.topic !== topic);
  }

  /**
   * Get all topics
   */
  public getTopics(): string[] {
    return Array.from(new Set(this.eventHistory.map(event => event.topic)));
  }

  /**
   * Get all subscriptions
   */
  public getSubscriptions(): { id: string; topic: string }[] {
    const subscriptions: { id: string; topic: string }[] = [];
    for (const [topic] of Array.from(this.subscribers.entries())) {
      subscriptions.push({
        id: `${topic}-subscription`,
        topic
      });
    }
    return subscriptions;
  }

  /**
   * Get version
   */
  public getVersion(): number {
    return 1;
  }

  /**
   * Get parent version
   */
  public getParentVersion(): number | undefined {
    return this.parentBlackboard ? 1 : undefined;
  }

  /**
   * Get causal hash
   */
  public getCausalHash(): string | undefined {
    return undefined;
  }

  /**
   * Get version history
   */
  public getVersionHistory(): Array<{ version: number; timestamp: number; causalHash: string; changes: string[] }> {
    return [];
  }

  /**
   * Create snapshot
   */
  public createSnapshot(): {
    version: number;
    timestamp: number;
    causalHash: string;
    agents: Map<string, AgentRegistration>;
    messages: Map<string, AgentMessage>;
    tasks: Map<string, AgentTask[]>;
    sharedData: Map<string, { value: any; metadata?: Record<string, any>; version: number; timestamp: number; causalHash?: string }>;
  } {
    return {
      version: 1,
      timestamp: Date.now(),
      causalHash: '',
      agents: new Map(this.agents),
      messages: new Map(this.messages),
      tasks: new Map(this.tasks),
      sharedData: new Map(Array.from(this.sharedData.entries()).map(([key, data]) => [
        key,
        {
          ...data,
          version: 1,
          timestamp: Date.now()
        }
      ]))
    };
  }

  /**
   * Restore from snapshot
   */
  public restoreFromSnapshot(snapshot: any): void {
    this.agents = new Map(snapshot.agents);
    this.messages = new Map(snapshot.messages);
    this.tasks = new Map(snapshot.tasks);
    this.sharedData = new Map();
    for (const [key, data] of snapshot.sharedData.entries()) {
      this.sharedData.set(key, {
        value: data.value,
        metadata: data.metadata
      });
    }
  }

  /**
   * Merge with another blackboard
   */
  public mergeWith(otherBlackboard: BlackboardInterface): void {
    // Simple merge implementation
    const otherSnapshot = otherBlackboard.createSnapshot();

    // Merge agents
    for (const [id, agent] of Array.from(otherSnapshot.agents.entries())) {
      this.agents.set(id, agent);
    }

    // Merge messages
    for (const [id, message] of Array.from(otherSnapshot.messages.entries())) {
      this.messages.set(id, message);
    }

    // Merge tasks
    for (const [id, tasks] of Array.from(otherSnapshot.tasks.entries())) {
      this.tasks.set(id, tasks);
    }

    // Merge shared data
    for (const [key, data] of Array.from(otherSnapshot.sharedData.entries())) {
      this.sharedData.set(key, {
        value: data.value,
        metadata: data.metadata
      });
    }
  }

  /**
   * Export for sync
   */
  public exportForSync(): {
    version: number;
    timestamp: number;
    causalHash: string;
    agents: Record<string, AgentRegistration>;
    messages: Record<string, AgentMessage>;
    tasks: Record<string, AgentTask[]>;
    sharedData: Record<string, { value: any; metadata?: Record<string, any>; version: number; timestamp: number; causalHash?: string }>;
  } {
    const snapshot = this.createSnapshot();
    return {
      version: snapshot.version,
      timestamp: snapshot.timestamp,
      causalHash: snapshot.causalHash,
      agents: Object.fromEntries(snapshot.agents),
      messages: Object.fromEntries(snapshot.messages),
      tasks: Object.fromEntries(snapshot.tasks),
      sharedData: Object.fromEntries(snapshot.sharedData)
    };
  }

  /**
   * Import from sync
   */
  public importFromSync(data: any): void {
    this.agents = new Map(Object.entries(data.agents));
    this.messages = new Map(Object.entries(data.messages));
    this.tasks = new Map(Object.entries(data.tasks));
    this.sharedData = new Map();
    for (const [key, sharedDataItem] of Array.from(Object.entries(data.sharedData))) {
      const item = sharedDataItem as any;
      this.sharedData.set(key, {
        value: item.value,
        metadata: item.metadata
      });
    }
  }

  /**
   * Get blackboard statistics
   */
  public getStatistics(): any {
    return {
      id: this.id,
      name: this.name,
      level: this.level,
      agentCount: this.agents.size,
      messageCount: this.messages.size,
      taskCount: Array.from(this.tasks.values()).reduce((sum, tasks) => sum + tasks.length, 0),
      sharedDataCount: this.sharedData.size,
      subscriberCount: Array.from(this.subscribers.values()).reduce((sum, callbacks) => sum + callbacks.length, 0),
      childBlackboardCount: this.childBlackboards.size,
      hasParent: this.parentBlackboard !== null,
      eventHistorySize: this.eventHistory.length,
      timestamp: Date.now()
    };
  }

  /**
   * Initialize the blackboard
   */
  public abstract initialize(): Promise<void>;

  /**
   * Shutdown the blackboard
   */
  public abstract shutdown(): Promise<void>;
}

