import React, { useCallback, useEffect } from 'react';
import * as Sentry from '@sentry/react';
import { monitoringService } from '../services/monitoring';

/**
 * Custom hook for Sentry integration in React components
 * Provides easy access to error tracking, performance monitoring, and user analytics
 */
export const useSentry = () => {
  /**
   * Capture an error with additional context
   */
  const captureError = useCallback((error: Error, context?: Record<string, any>) => {
    Sentry.withScope((scope) => {
      if (context) {
        scope.setContext('component_context', context);
      }
      scope.setTag('source', 'react_component');
      Sentry.captureException(error);
    });
  }, []);

  /**
   * Capture a message with level and context
   */
  const captureMessage = useCallback((
    message: string, 
    level: 'info' | 'warning' | 'error' = 'info',
    context?: Record<string, any>
  ) => {
    Sentry.withScope((scope) => {
      if (context) {
        scope.setContext('message_context', context);
      }
      scope.setLevel(level);
      scope.setTag('source', 'react_component');
      Sentry.captureMessage(message);
    });
  }, []);

  /**
   * Add a breadcrumb for tracking user actions
   */
  const addBreadcrumb = useCallback((
    message: string,
    category: string = 'user',
    data?: Record<string, any>
  ) => {
    Sentry.addBreadcrumb({
      message,
      category,
      level: 'info',
      data,
    });
  }, []);

  /**
   * Track a user interaction specific to Alice AGI
   */
  const trackAliceInteraction = useCallback((
    action: string,
    component: string,
    data?: Record<string, any>
  ) => {
    // Track in monitoring service
    monitoringService.trackUserInteraction({
      type: 'chat_message',
      element: component,
      timestamp: Date.now(),
      metadata: { action, ...data },
    });

    // Add Sentry breadcrumb
    addBreadcrumb(`Alice: ${action}`, 'alice_interaction', {
      component,
      ...data,
    });
  }, [addBreadcrumb]);

  /**
   * Track chat-related events
   */
  const trackChatEvent = useCallback((
    eventType: 'message_sent' | 'message_received' | 'chat_created' | 'chat_deleted',
    data?: Record<string, any>
  ) => {
    monitoringService.trackAliceEvent(`chat_${eventType}`, {
      timestamp: Date.now(),
      ...data,
    });

    addBreadcrumb(`Chat: ${eventType}`, 'chat', data);
  }, [addBreadcrumb]);

  /**
   * Track system events
   */
  const trackSystemEvent = useCallback((
    eventType: 'system_status_check' | 'system_error' | 'system_recovery',
    data?: Record<string, any>
  ) => {
    monitoringService.trackAliceEvent(`system_${eventType}`, {
      timestamp: Date.now(),
      ...data,
    });

    addBreadcrumb(`System: ${eventType}`, 'system', data);
  }, [addBreadcrumb]);

  /**
   * Track performance metrics for a specific component
   */
  const trackComponentPerformance = useCallback((
    componentName: string,
    operation: string,
    duration: number,
    metadata?: Record<string, any>
  ) => {
    const performanceData = {
      component: componentName,
      operation,
      duration,
      timestamp: Date.now(),
      ...metadata,
    };

    // Add to Sentry as breadcrumb
    addBreadcrumb(
      `Performance: ${componentName}.${operation} (${duration.toFixed(2)}ms)`,
      'performance',
      performanceData
    );

    // Track in monitoring service
    monitoringService.trackAliceEvent('component_performance', performanceData);

    // If duration is concerning, capture as performance issue
    if (duration > 1000) { // More than 1 second
      captureMessage(
        `Slow component operation: ${componentName}.${operation}`,
        'warning',
        performanceData
      );
    }
  }, [addBreadcrumb, captureMessage]);

  /**
   * Set user context
   */
  const setUser = useCallback((user: {
    id: string;
    username?: string;
    email?: string;
    [key: string]: any;
  }) => {
    Sentry.setUser(user);
  }, []);

  /**
   * Set additional context
   */
  const setContext = useCallback((key: string, context: Record<string, any>) => {
    Sentry.setContext(key, context);
  }, []);

  /**
   * Set tags for filtering
   */
  const setTag = useCallback((key: string, value: string) => {
    Sentry.setTag(key, value);
  }, []);

  /**
   * Start a transaction for performance monitoring
   */
  const startTransaction = useCallback((name: string, operation: string = 'navigation') => {
    return Sentry.startTransaction({
      name,
      op: operation,
    });
  }, []);

  /**
   * Measure and track a function's performance
   */
  const measurePerformance = useCallback(async <T>(
    name: string,
    fn: () => Promise<T> | T,
    context?: Record<string, any>
  ): Promise<T> => {
    const startTime = performance.now();
    const transaction = startTransaction(name, 'function');

    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      trackComponentPerformance('function', name, duration, context);
      transaction.setStatus('ok');
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      transaction.setStatus('internal_error');
      captureError(error as Error, {
        function: name,
        duration,
        ...context,
      });
      
      throw error;
    } finally {
      transaction.finish();
    }
  }, [startTransaction, trackComponentPerformance, captureError]);

  /**
   * Create a Sentry-wrapped component for automatic error boundary
   */
  const withSentryErrorBoundary = useCallback(<P extends object>(
    Component: React.ComponentType<P>,
    options?: {
      fallback?: React.ComponentType<any>;
      beforeCapture?: (scope: Sentry.Scope) => void;
    }
  ) => {
    return Sentry.withErrorBoundary(Component, {
      fallback: options?.fallback || (() =>
        React.createElement('div', { className: 'p-4 text-center text-red-600' },
          React.createElement('p', null, 'Something went wrong in this component.')
        )
      ),
      beforeCapture: (scope) => {
        scope.setTag('error_boundary', 'sentry_wrapped');
        options?.beforeCapture?.(scope);
      },
    });
  }, []);

  return {
    // Error tracking
    captureError,
    captureMessage,
    addBreadcrumb,
    
    // Alice-specific tracking
    trackAliceInteraction,
    trackChatEvent,
    trackSystemEvent,
    
    // Performance tracking
    trackComponentPerformance,
    measurePerformance,
    startTransaction,
    
    // Context and user management
    setUser,
    setContext,
    setTag,
    
    // Component wrapping
    withSentryErrorBoundary,
  };
};

/**
 * Hook for tracking component lifecycle with Sentry
 */
export const useSentryComponentTracking = (componentName: string) => {
  const { trackComponentPerformance, addBreadcrumb } = useSentry();

  useEffect(() => {
    const mountTime = performance.now();
    
    addBreadcrumb(`Component mounted: ${componentName}`, 'lifecycle');

    return () => {
      const unmountTime = performance.now();
      const lifetime = unmountTime - mountTime;
      
      trackComponentPerformance(componentName, 'lifetime', lifetime);
      addBreadcrumb(`Component unmounted: ${componentName}`, 'lifecycle', {
        lifetime: lifetime.toFixed(2),
      });
    };
  }, [componentName, trackComponentPerformance, addBreadcrumb]);
};

/**
 * Hook for tracking render performance
 */
export const useSentryRenderTracking = (componentName: string) => {
  const { trackComponentPerformance } = useSentry();

  useEffect(() => {
    const renderTime = performance.now();
    
    // Track render time on next tick
    setTimeout(() => {
      const duration = performance.now() - renderTime;
      trackComponentPerformance(componentName, 'render', duration);
    }, 0);
  });
};

export default useSentry;
