{"name": "@repo/mcp", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "react-router dev", "build": "react-router build", "cf-typegen": "wrangler types", "deploy": "bun run build && wrangler deploy", "preview": "bun run build && vite preview", "typecheck": "wrangler types && react-router typegen && tsc -b"}, "dependencies": {"@hono-rate-limiter/cloudflare": "^0.2.2", "@standard-community/standard-json": "^0.1.2", "@supermemory/sdk": "^0.1.0-alpha.8", "@total-typescript/tsconfig": "^1.0.4", "@valibot/to-json-schema": "^1.0.0", "effect": "^3.14.8", "hono": "^4.7.6", "hono-rate-limiter": "^0.4.2", "isbot": "^5.1.26", "muppet": "^0.2.1", "nanoid": "^5.1.5", "pino": "^9.6.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.0", "react-router-dom": "^7.5.0", "supermemory": "^3.0.0-alpha.14", "winston": "^3.17.0", "zod": "^3.24.2"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.0.7", "@cloudflare/workers-types": "^4.20250415.0", "@react-router/dev": "^7.5.0", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20.17.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "typescript": "^5.8.3", "vite": "^6.2.6", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.11.1"}}