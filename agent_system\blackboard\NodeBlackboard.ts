/**
 * NodeBlackboard.ts
 * 
 * A blackboard for a node in the AliceNet system, shared among agents on the node.
 * This is the second level in the hierarchical blackboard system.
 */

import { HierarchicalBlackboard, HierarchicalBlackboardConfig, PropagationDirection } from './HierarchicalBlackboard';
import { v4 as uuidv4 } from 'uuid';
import { BiologicalBlackboardIntegrator } from './BiologicalBlackboardIntegrator';

/**
 * Node blackboard configuration
 */
export interface NodeBlackboardConfig extends HierarchicalBlackboardConfig {
  nodeId: string;
  enableResourceTracking?: boolean;
  enableAgentCoordination?: boolean;
  enableNodeMetrics?: boolean;
  enableNodeHealth?: boolean;
  metricsUpdateIntervalMs?: number;
}

/**
 * Node blackboard class
 */
export class NodeBlackboard extends HierarchicalBlackboard {
  protected config: NodeBlackboardConfig;
  private nodeId: string;
  private resources: Map<string, { available: number; total: number; unit: string }> = new Map();
  private nodeMetrics: Map<string, number> = new Map();
  private nodeHealth: {
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: Array<{ id: string; description: string; severity: 'low' | 'medium' | 'high'; timestamp: Date }>;
  } = {
    status: 'healthy',
    issues: []
  };
  private metricsUpdateIntervalId: NodeJS.Timeout | null = null;
  private biologicalIntegrator: BiologicalBlackboardIntegrator | null = null;
  
  /**
   * Constructor
   */
  constructor(config: NodeBlackboardConfig) {
    // Create default propagation policies for node blackboard
    const defaultPropagationPolicies = [
      {
        direction: PropagationDirection.UP,
        topicPatterns: [
          'node:.*',
          'agent:task_completed',
          'agent:status_updated',
          'biological:.*',
          'viral_ecology:.*',
          'civilization:.*'
        ],
        excludePatterns: [
          'node:private_.*',
          'node:resource_.*'
        ]
      },
      {
        direction: PropagationDirection.DOWN,
        topicPatterns: [
          'agent:task_assigned',
          'system:.*',
          'global:.*',
          'regional:.*'
        ]
      }
    ];
    
    // Merge with provided policies
    const mergedPolicies = [
      ...defaultPropagationPolicies,
      ...(config.propagationPolicies || [])
    ];
    
    // Call parent constructor
    super({
      ...config,
      level: 'node',
      propagationPolicies: mergedPolicies
    });
    
    this.nodeId = config.nodeId;
    
    // Set default configuration
    this.config = {
      ...config,
      enableResourceTracking: config.enableResourceTracking !== undefined ? config.enableResourceTracking : true,
      enableAgentCoordination: config.enableAgentCoordination !== undefined ? config.enableAgentCoordination : true,
      enableNodeMetrics: config.enableNodeMetrics !== undefined ? config.enableNodeMetrics : true,
      enableNodeHealth: config.enableNodeHealth !== undefined ? config.enableNodeHealth : true,
      metricsUpdateIntervalMs: config.metricsUpdateIntervalMs || 60000 // 1 minute
    };
    
    // Initialize biological integration if enabled
    if (config.enableBiologicalIntegration) {
      this.biologicalIntegrator = new BiologicalBlackboardIntegrator(this, {
        blackboardId: this.id,
        blackboardLevel: 'node',
        nodeId: this.nodeId
      });
    }
  }
  
  /**
   * Get node ID
   */
  public getNodeId(): string {
    return this.nodeId;
  }

  /**
   * Get configuration
   */
  public getConfig(): NodeBlackboardConfig {
    return this.config;
  }
  
  /**
   * Update resource
   */
  public updateResource(
    resourceId: string,
    available: number,
    total: number,
    unit: string
  ): void {
    this.resources.set(resourceId, { available, total, unit });
    
    // Notify subscribers
    this.notifySubscribers('node:resource_updated', {
      nodeId: this.nodeId,
      resourceId,
      available,
      total,
      unit,
      timestamp: Date.now()
    });
  }
  
  /**
   * Get resource
   */
  public getResource(resourceId: string): { available: number; total: number; unit: string } | undefined {
    return this.resources.get(resourceId);
  }
  
  /**
   * Get all resources
   */
  public getAllResources(): Map<string, { available: number; total: number; unit: string }> {
    return new Map(this.resources);
  }
  
  /**
   * Update node metric
   */
  public updateNodeMetric(metricId: string, value: number): void {
    this.nodeMetrics.set(metricId, value);
    
    // Notify subscribers
    this.notifySubscribers('node:metric_updated', {
      nodeId: this.nodeId,
      metricId,
      value,
      timestamp: Date.now()
    });
  }
  
  /**
   * Get node metric
   */
  public getNodeMetric(metricId: string): number | undefined {
    return this.nodeMetrics.get(metricId);
  }
  
  /**
   * Get all node metrics
   */
  public getAllNodeMetrics(): Map<string, number> {
    return new Map(this.nodeMetrics);
  }
  
  /**
   * Update node health
   */
  public updateNodeHealth(status: 'healthy' | 'degraded' | 'unhealthy'): void {
    this.nodeHealth.status = status;
    
    // Notify subscribers
    this.notifySubscribers('node:health_updated', {
      nodeId: this.nodeId,
      status,
      timestamp: Date.now()
    });
  }
  
  /**
   * Add node health issue
   */
  public addNodeHealthIssue(
    description: string,
    severity: 'low' | 'medium' | 'high'
  ): string {
    const issueId = uuidv4();
    
    this.nodeHealth.issues.push({
      id: issueId,
      description,
      severity,
      timestamp: new Date()
    });
    
    // Update node health status based on issues
    if (severity === 'high') {
      this.updateNodeHealth('unhealthy');
    } else if (severity === 'medium' && this.nodeHealth.status === 'healthy') {
      this.updateNodeHealth('degraded');
    }
    
    // Notify subscribers
    this.notifySubscribers('node:health_issue_added', {
      nodeId: this.nodeId,
      issueId,
      description,
      severity,
      timestamp: Date.now()
    });
    
    return issueId;
  }
  
  /**
   * Resolve node health issue
   */
  public resolveNodeHealthIssue(issueId: string): boolean {
    const issueIndex = this.nodeHealth.issues.findIndex(issue => issue.id === issueId);
    
    if (issueIndex === -1) {
      return false;
    }
    
    const issue = this.nodeHealth.issues[issueIndex];
    this.nodeHealth.issues.splice(issueIndex, 1);
    
    // Recalculate node health status
    if (this.nodeHealth.issues.length === 0) {
      this.updateNodeHealth('healthy');
    } else if (this.nodeHealth.issues.every(issue => issue.severity === 'low')) {
      this.updateNodeHealth('healthy');
    } else if (this.nodeHealth.issues.every(issue => issue.severity !== 'high')) {
      this.updateNodeHealth('degraded');
    }
    
    // Notify subscribers
    this.notifySubscribers('node:health_issue_resolved', {
      nodeId: this.nodeId,
      issueId,
      timestamp: Date.now()
    });
    
    return true;
  }
  
  /**
   * Get node health
   */
  public getNodeHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    issues: Array<{ id: string; description: string; severity: 'low' | 'medium' | 'high'; timestamp: Date }>;
  } {
    return {
      status: this.nodeHealth.status,
      issues: [...this.nodeHealth.issues]
    };
  }
  
  /**
   * Start metrics update interval
   */
  private startMetricsUpdateInterval(): void {
    if (this.metricsUpdateIntervalId) {
      clearInterval(this.metricsUpdateIntervalId);
    }
    
    this.metricsUpdateIntervalId = setInterval(() => {
      this.updateNodeMetrics();
    }, this.config.metricsUpdateIntervalMs);
  }
  
  /**
   * Stop metrics update interval
   */
  private stopMetricsUpdateInterval(): void {
    if (this.metricsUpdateIntervalId) {
      clearInterval(this.metricsUpdateIntervalId);
      this.metricsUpdateIntervalId = null;
    }
  }
  
  /**
   * Update node metrics
   */
  private updateNodeMetrics(): void {
    if (!this.config.enableNodeMetrics) {
      return;
    }
    
    // Update agent count metric
    this.updateNodeMetric('agent_count', this.agents.size);
    
    // Update message count metric
    this.updateNodeMetric('message_count', this.messages.size);
    
    // Update task count metric
    const taskCount = Array.from(this.tasks.values()).reduce((sum, tasks) => sum + tasks.length, 0);
    this.updateNodeMetric('task_count', taskCount);
    
    // Update shared data count metric
    this.updateNodeMetric('shared_data_count', this.sharedData.size);
    
    // Update child blackboard count metric
    this.updateNodeMetric('child_blackboard_count', this.childBlackboards.size);
    
    // Update resource utilization metrics
    for (const [resourceId, resource] of this.resources.entries()) {
      const utilization = resource.total > 0 ? (resource.total - resource.available) / resource.total : 0;
      this.updateNodeMetric(`resource_utilization_${resourceId}`, utilization);
    }
    
    // Publish metrics updated event
    this.publish('node:metrics_updated', {
      nodeId: this.nodeId,
      metrics: Object.fromEntries(this.nodeMetrics),
      timestamp: Date.now()
    });
  }
  
  /**
   * Initialize the blackboard
   */
  public async initialize(): Promise<void> {
    console.log(`Initializing node blackboard for node ${this.nodeId}`);
    
    // Initialize biological integration
    if (this.biologicalIntegrator) {
      await this.biologicalIntegrator.initialize();
    }
    
    // Start metrics update interval if enabled
    if (this.config.enableNodeMetrics) {
      this.startMetricsUpdateInterval();
    }
    
    // Register self with parent blackboard if available
    if (this.parentBlackboard) {
      this.parentBlackboard.receiveFromChild(this.id, 'blackboard:initialized', {
        blackboardId: this.id,
        blackboardLevel: 'node',
        nodeId: this.nodeId,
        timestamp: Date.now()
      });
    }
    
    // Publish initialization event
    this.publish('blackboard:initialized', {
      blackboardId: this.id,
      blackboardLevel: 'node',
      nodeId: this.nodeId,
      timestamp: Date.now()
    });
  }
  
  /**
   * Shutdown the blackboard
   */
  public async shutdown(): Promise<void> {
    console.log(`Shutting down node blackboard for node ${this.nodeId}`);
    
    // Stop metrics update interval
    this.stopMetricsUpdateInterval();
    
    // Shutdown biological integration
    if (this.biologicalIntegrator) {
      await this.biologicalIntegrator.shutdown();
    }
    
    // Notify parent blackboard if available
    if (this.parentBlackboard) {
      this.parentBlackboard.receiveFromChild(this.id, 'blackboard:shutdown', {
        blackboardId: this.id,
        blackboardLevel: 'node',
        nodeId: this.nodeId,
        timestamp: Date.now()
      });
    }
    
    // Publish shutdown event
    this.publish('blackboard:shutdown', {
      blackboardId: this.id,
      blackboardLevel: 'node',
      nodeId: this.nodeId,
      timestamp: Date.now()
    });
  }
}

