/**
 * unified_consciousness_interface.test.ts
 * 
 * Tests for the Unified Consciousness Interface.
 */

import { UnifiedConsciousnessInterface } from '../unified_consciousness_interface';
import { BlackboardSystem } from '../../blackboard/blackboard_system';
import { MemorySystem } from '../../memory/memory_system';
import { GlobalHeartbeatSystem } from '../../core/global_heartbeat_system';
import { ConsciousnessLevel } from '../consciousness_types';

// Mock dependencies
jest.mock('../../blackboard/blackboard_system');
jest.mock('../../memory/memory_system');
jest.mock('../../core/global_heartbeat_system');

describe('UnifiedConsciousnessInterface', () => {
  let blackboard: jest.Mocked<BlackboardSystem>;
  let memory: jest.Mocked<MemorySystem>;
  let heartbeat: jest.Mocked<GlobalHeartbeatSystem>;
  let consciousnessInterface: UnifiedConsciousnessInterface;
  
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock instances
    blackboard = new BlackboardSystem() as jest.Mocked<BlackboardSystem>;

    // Create mock memory forest for MemorySystem constructor
    const mockMemoryForest = {} as any;
    memory = new MemorySystem(blackboard, mockMemoryForest) as jest.Mocked<MemorySystem>;

    // Use getInstance for singleton GlobalHeartbeatSystem
    heartbeat = GlobalHeartbeatSystem.getInstance() as jest.Mocked<GlobalHeartbeatSystem>;
    
    // Mock methods
    blackboard.writeToBlackboard = jest.fn();
    blackboard.readFromBlackboard = jest.fn().mockReturnValue(null);
    memory.get = jest.fn().mockResolvedValue([]);
    memory.set = jest.fn().mockResolvedValue(undefined);
    heartbeat.on = jest.fn();
    
    // Create interface instance
    consciousnessInterface = UnifiedConsciousnessInterface.getInstance(
      blackboard,
      {
        defaultConsciousnessLevel: ConsciousnessLevel.NORMAL,
        enableConsciousnessEvents: true,
        enableConsciousnessTransitions: true,
        enableConsciousnessMonitoring: true,
        logLevel: 'debug'
      },
      memory,
      heartbeat
    );
  });
  
  describe('Initialization', () => {
    it('should initialize the interface', async () => {
      await consciousnessInterface.initialize();
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_initializing',
        expect.objectContaining({
          options: expect.objectContaining({
            defaultConsciousnessLevel: ConsciousnessLevel.NORMAL,
            enableConsciousnessEvents: true,
            enableConsciousnessTransitions: true,
            enableConsciousnessMonitoring: true
          })
        })
      );
      
      // Check if memory was accessed
      expect(memory.get).toHaveBeenCalledWith('consciousness_history');
      
      // Check if heartbeat event listener was registered
      expect(heartbeat.on).toHaveBeenCalledWith('heartbeat', expect.any(Function));
      
      // Check if blackboard was updated again
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_initialized',
        expect.objectContaining({
          options: expect.objectContaining({
            defaultConsciousnessLevel: ConsciousnessLevel.NORMAL,
            enableConsciousnessEvents: true,
            enableConsciousnessTransitions: true,
            enableConsciousnessMonitoring: true
          })
        })
      );
    });
    
    it('should not initialize twice', async () => {
      await consciousnessInterface.initialize();
      
      // Reset mock
      blackboard.writeToBlackboard.mockClear();
      
      // Try to initialize again
      await consciousnessInterface.initialize();
      
      // Check if blackboard was not updated
      expect(blackboard.writeToBlackboard).not.toHaveBeenCalled();
    });
  });
  
  describe('Start and Stop', () => {
    it('should start the interface', async () => {
      await consciousnessInterface.initialize();
      consciousnessInterface.start();
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_starting',
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );
      
      // Check if blackboard was updated again
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_started',
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );
    });
    
    it('should not start if not initialized', () => {
      expect(() => consciousnessInterface.start()).toThrow('Unified Consciousness Interface must be initialized before starting');
    });
    
    it('should not start twice', async () => {
      await consciousnessInterface.initialize();
      consciousnessInterface.start();
      
      // Reset mock
      blackboard.writeToBlackboard.mockClear();
      
      // Try to start again
      consciousnessInterface.start();
      
      // Check if blackboard was not updated
      expect(blackboard.writeToBlackboard).not.toHaveBeenCalled();
    });
    
    it('should stop the interface', async () => {
      await consciousnessInterface.initialize();
      consciousnessInterface.start();
      consciousnessInterface.stop();
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_stopping',
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );
      
      // Check if blackboard was updated again
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'unified_consciousness_interface_stopped',
        expect.objectContaining({
          timestamp: expect.any(Number)
        })
      );
    });
    
    it('should not stop if not running', async () => {
      await consciousnessInterface.initialize();
      
      // Reset mock
      blackboard.writeToBlackboard.mockClear();
      
      // Try to stop
      consciousnessInterface.stop();
      
      // Check if blackboard was not updated
      expect(blackboard.writeToBlackboard).not.toHaveBeenCalled();
    });
  });
  
  describe('Consciousness State', () => {
    beforeEach(async () => {
      await consciousnessInterface.initialize();
      consciousnessInterface.start();
    });
    
    it('should get current consciousness state', () => {
      const state = consciousnessInterface.getCurrentState();
      
      // Check state properties
      expect(state).toEqual(expect.objectContaining({
        level: ConsciousnessLevel.NORMAL,
        metrics: expect.objectContaining({
          coherence: expect.any(Number),
          integration: expect.any(Number),
          differentiation: expect.any(Number)
        }),
        activeProcesses: expect.any(Array),
        focusedEntities: expect.any(Array)
      }));
    });
    
    it('should set consciousness level', () => {
      const newState = consciousnessInterface.setConsciousnessLevel(
        ConsciousnessLevel.HEIGHTENED,
        'Test reason'
      );
      
      // Check if level was updated
      expect(newState.level).toBe(ConsciousnessLevel.HEIGHTENED);
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'current_consciousness_state',
        expect.objectContaining({
          level: ConsciousnessLevel.HEIGHTENED
        })
      );
      
      // Check if blackboard was updated with transition event
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'consciousness_transition',
        expect.objectContaining({
          type: 'transition',
          fromState: expect.objectContaining({
            level: ConsciousnessLevel.NORMAL
          }),
          toState: expect.objectContaining({
            level: ConsciousnessLevel.HEIGHTENED
          }),
          metadata: expect.objectContaining({
            reason: 'Test reason',
            manual: true
          })
        })
      );
    });
    
    it('should set focused entities', () => {
      const entities = ['entity1', 'entity2'];
      const newState = consciousnessInterface.setFocusedEntities(
        entities,
        'Test reason'
      );
      
      // Check if focused entities were updated
      expect(newState.focusedEntities).toEqual(entities);
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'current_consciousness_state',
        expect.objectContaining({
          focusedEntities: entities
        })
      );
      
      // Check if blackboard was updated with event
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'consciousness_event',
        expect.objectContaining({
          type: 'focus_change',
          fromState: expect.objectContaining({
            focusedEntities: []
          }),
          toState: expect.objectContaining({
            focusedEntities: entities
          }),
          metadata: expect.objectContaining({
            reason: 'Test reason',
            manual: true,
            previousFocus: [],
            newFocus: entities
          })
        })
      );
    });
    
    it('should raise consciousness event', () => {
      const event = consciousnessInterface.raiseConsciousnessEvent(
        'test_event',
        { test: 'data' }
      );
      
      // Check event properties
      expect(event).toEqual(expect.objectContaining({
        type: 'test_event',
        metadata: expect.objectContaining({
          test: 'data'
        })
      }));
      
      // Check if blackboard was updated
      expect(blackboard.writeToBlackboard).toHaveBeenCalledWith(
        'consciousness',
        'consciousness_event',
        expect.objectContaining({
          type: 'test_event',
          metadata: expect.objectContaining({
            test: 'data'
          })
        })
      );
    });
    
    it('should get consciousness history', () => {
      // Set consciousness level to create history
      consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.HEIGHTENED);
      consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.MINIMAL);
      
      const history = consciousnessInterface.getConsciousnessHistory();
      
      // Check history
      expect(history).toHaveLength(3); // Initial state + 2 changes
      expect(history[0].level).toBe(ConsciousnessLevel.NORMAL);
      expect(history[1].level).toBe(ConsciousnessLevel.HEIGHTENED);
      expect(history[2].level).toBe(ConsciousnessLevel.MINIMAL);
    });
    
    it('should get consciousness history for time range', () => {
      // Mock Date.now to control timestamps
      const originalNow = Date.now;
      const mockNow = jest.fn();
      
      try {
        // First state at time 1000
        mockNow.mockReturnValue(1000);
        global.Date.now = mockNow;
        const state1 = consciousnessInterface.getCurrentState();
        
        // Second state at time 2000
        mockNow.mockReturnValue(2000);
        global.Date.now = mockNow;
        consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.HEIGHTENED);
        
        // Third state at time 3000
        mockNow.mockReturnValue(3000);
        global.Date.now = mockNow;
        consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.MINIMAL);
        
        // Get history for time range 1500-2500
        const history = consciousnessInterface.getConsciousnessHistoryForTimeRange(1500, 2500);
        
        // Check history
        expect(history).toHaveLength(1);
        expect(history[0].timestamp).toBe(2000);
        expect(history[0].level).toBe(ConsciousnessLevel.HEIGHTENED);
      } finally {
        global.Date.now = originalNow;
      }
    });
    
    it('should get consciousness history for level', () => {
      // Set consciousness level to create history
      consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.HEIGHTENED);
      consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.MINIMAL);
      consciousnessInterface.setConsciousnessLevel(ConsciousnessLevel.HEIGHTENED);
      
      const history = consciousnessInterface.getConsciousnessHistoryForLevel(ConsciousnessLevel.HEIGHTENED);
      
      // Check history
      expect(history).toHaveLength(2);
      expect(history[0].level).toBe(ConsciousnessLevel.HEIGHTENED);
      expect(history[1].level).toBe(ConsciousnessLevel.HEIGHTENED);
    });
  });
});
