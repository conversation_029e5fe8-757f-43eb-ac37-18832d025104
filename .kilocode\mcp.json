{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": "100"}}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "playwrightEimsto": {"command": "npx", "args": ["@eimsto/playwright-mcp-server"]}, "playwrightExec": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server"]}, "vscode": {"command": "npx", "args": ["vscode-mcp-server"]}, "airtable": {"command": "npx", "args": ["@felores/airtable-mcp-server"], "env": {"AIRTABLE_API_KEY": "your-airtable-key-here"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}}}