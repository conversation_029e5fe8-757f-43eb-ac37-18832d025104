# Supermemory MCP - Universal Memory across LLMs

[![Universal Memory MCP - Your memories, in every LLM you use. | Product Hunt](https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=954861&theme=neutral&period=daily&t=1749339045428)](https://www.producthunt.com/products/supermemory?embed=true&utm_source=badge-top-post-badge&utm_medium=badge&utm_source=badge-universal-memory-mcp)

Read a detailed blog about it - https://supermemory.ai/blog/the-ux-and-technicalities-of-awesome-mcps 

**Your memories are in ChatGPT... But nowhere else. Universal Memory MCP makes your memories available to every single LLM. No logins or paywall. One command to set it up.**

Which means you can carry your memories to any MCP client. and it just works!

## Demo (Click on the image for video!)

[![Demo Video](./public/og-image.png)](https://youtu.be/ST6BR3vT5Xw)

## Getting Started

To get started, just visit https://mcp.supermemory.ai, and follow the instructions on the page.

## Features

- 🚀 Built on top of the [Supermemory API](https://supermemory.ai), extremely fast and scalable.
- ✅ No login required
- 😱 Completely free to use
- Extremely simple setup.

## Self-hosting

To self host, get an API key at https://console.supermemory.ai, and then simply add it in the `.env` file with `SUPERMEMORY_API_KEY=`
