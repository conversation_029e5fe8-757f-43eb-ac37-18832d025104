{"extends": "./tsconfig.json", "include": [".react-router/types/**/*", "app/**/*", "app/**/.server/**/*", "app/**/.client/**/*", "workers/**/*", "worker-configuration.d.ts"], "compilerOptions": {"composite": true, "strict": true, "lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["vite/client"], "incremental": true, "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "baseUrl": ".", "rootDirs": [".", "./.react-router/types"], "paths": {"~/*": ["./app/*"]}, "esModuleInterop": true, "resolveJsonModule": true}}